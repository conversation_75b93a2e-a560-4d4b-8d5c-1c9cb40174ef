[{"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "GetBlogs", "RelativePath": "api/v1/admin/AdminBlogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Author", "Type": "System.String", "IsRequired": false}, {"Name": "IsPublished", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PublishedDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PublishedDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "CreateBlog", "RelativePath": "api/v1/admin/AdminBlogs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Blog.CreateBlogRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "GetBlog", "RelativePath": "api/v1/admin/AdminBlogs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "UpdateBlog", "RelativePath": "api/v1/admin/AdminBlogs/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Blog.UpdateBlogRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "DeleteBlog", "RelativePath": "api/v1/admin/AdminBlogs/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "GetBlogComments", "RelativePath": "api/v1/admin/AdminBlogs/{id}/comments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "DeleteBlogComment", "RelativePath": "api/v1/admin/AdminBlogs/{id}/comments/{commentId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "commentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "ApproveBlogComment", "RelativePath": "api/v1/admin/AdminBlogs/{id}/comments/{commentId}/approve", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "commentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "RejectBlogComment", "RelativePath": "api/v1/admin/AdminBlogs/{id}/comments/{commentId}/reject", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "commentId", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "UploadBlogCoverImage", "RelativePath": "api/v1/admin/AdminBlogs/{id}/cover-image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "DeleteBlogCoverImage", "RelativePath": "api/v1/admin/AdminBlogs/{id}/cover-image", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "FeatureBlog", "RelativePath": "api/v1/admin/AdminBlogs/{id}/feature", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "PublishBlog", "RelativePath": "api/v1/admin/AdminBlogs/{id}/publish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "GetBlogStatistics", "RelativePath": "api/v1/admin/AdminBlogs/{id}/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "UnfeatureBlog", "RelativePath": "api/v1/admin/AdminBlogs/{id}/unfeature", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "UnpublishBlog", "RelativePath": "api/v1/admin/AdminBlogs/{id}/unpublish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "BulkUpdateBlogs", "RelativePath": "api/v1/admin/AdminBlogs/bulk-update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.BulkUpdateBlogsRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "GetBlogCategories", "RelativePath": "api/v1/admin/AdminBlogs/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "CreateBlogCategory", "RelativePath": "api/v1/admin/AdminBlogs/categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Blog.CreateBlogCategoryRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "UpdateBlogCategory", "RelativePath": "api/v1/admin/AdminBlogs/categories/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Blog.UpdateBlogCategoryRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "DeleteBlogCategory", "RelativePath": "api/v1/admin/AdminBlogs/categories/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "ExportBlogs", "RelativePath": "api/v1/admin/AdminBlogs/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Format", "Type": "System.String", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Author", "Type": "System.String", "IsRequired": false}, {"Name": "IsPublished", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PublishedDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PublishedDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Fields", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "GetBlogTags", "RelativePath": "api/v1/admin/AdminBlogs/tags", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "CreateBlogTag", "RelativePath": "api/v1/admin/AdminBlogs/tags", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Blog.CreateBlogTagRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "UpdateBlogTag", "RelativePath": "api/v1/admin/AdminBlogs/tags/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Blog.UpdateBlogTagRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBlogsController", "Method": "DeleteBlogTag", "RelativePath": "api/v1/admin/AdminBlogs/tags/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetBookings", "RelativePath": "api/v1/admin/AdminBookings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "TripId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "BookingDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "BookingDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TripDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TripDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetBooking", "RelativePath": "api/v1/admin/AdminBookings/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "UpdateBooking", "RelativePath": "api/v1/admin/AdminBookings/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Booking.UpdateBookingRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "DeleteBooking", "RelativePath": "api/v1/admin/AdminBookings/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetBookingActivityLog", "RelativePath": "api/v1/admin/AdminBookings/{id}/activity", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "CancelBooking", "RelativePath": "api/v1/admin/AdminBookings/{id}/cancel", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.CancelBookingRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "ConfirmBooking", "RelativePath": "api/v1/admin/AdminBookings/{id}/confirm", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetBookingCustomer", "RelativePath": "api/v1/admin/AdminBookings/{id}/customer", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "AddBookingNote", "RelativePath": "api/v1/admin/AdminBookings/{id}/notes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.AddBookingNoteRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetBookingNotes", "RelativePath": "api/v1/admin/AdminBookings/{id}/notes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "UpdateBookingNote", "RelativePath": "api/v1/admin/AdminBookings/{id}/notes/{noteId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "noteId", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.UpdateBookingNoteRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "DeleteBookingNote", "RelativePath": "api/v1/admin/AdminBookings/{id}/notes/{noteId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "noteId", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetBookingPayment", "RelativePath": "api/v1/admin/AdminBookings/{id}/payment", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "ProcessRefund", "RelativePath": "api/v1/admin/AdminBookings/{id}/refund", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.ProcessRefundRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "SendBookingR<PERSON>inder", "RelativePath": "api/v1/admin/AdminBookings/{id}/reminder", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.SendReminderRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "UpdateBookingStatus", "RelativePath": "api/v1/admin/AdminBookings/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.UpdateBookingStatusRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetBookingTrip", "RelativePath": "api/v1/admin/AdminBookings/{id}/trip", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetBookingAnalytics", "RelativePath": "api/v1/admin/AdminBookings/analytics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "GroupBy", "Type": "System.String", "IsRequired": false}, {"Name": "TripId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "BulkUpdateBookings", "RelativePath": "api/v1/admin/AdminBookings/bulk-update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.BulkUpdateBookingsRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "ExportBookings", "RelativePath": "api/v1/admin/AdminBookings/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Format", "Type": "System.String", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "TripId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "BookingDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "BookingDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TripDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TripDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Fields", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "ExportBookingInvoices", "RelativePath": "api/v1/admin/AdminBookings/export-invoices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Format", "Type": "System.String", "IsRequired": false}, {"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "BookingId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "UserId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "IncludePaid", "Type": "System.Boolean", "IsRequired": false}, {"Name": "IncludeUnpaid", "Type": "System.Boolean", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetBookingReports", "RelativePath": "api/v1/admin/AdminBookings/reports", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ReportType", "Type": "System.String", "IsRequired": false}, {"Name": "Format", "Type": "System.String", "IsRequired": false}, {"Name": "TripId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetRevenueStatistics", "RelativePath": "api/v1/admin/AdminBookings/revenue-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "GroupBy", "Type": "System.String", "IsRequired": false}, {"Name": "TripId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PaymentMethod", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminBookingsController", "Method": "GetBookingStatistics", "RelativePath": "api/v1/admin/AdminBookings/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetApplicationLogs", "RelativePath": "api/v1/admin/AdminDashboard/application-logs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Level", "Type": "System.String", "IsRequired": false}, {"Name": "Source", "Type": "System.String", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetAuditLogs", "RelativePath": "api/v1/admin/AdminDashboard/audit-logs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "UserId", "Type": "System.String", "IsRequired": false}, {"Name": "Action", "Type": "System.String", "IsRequired": false}, {"Name": "EntityType", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetBackupStatus", "RelativePath": "api/v1/admin/AdminDashboard/backup-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetBookingTrends", "RelativePath": "api/v1/admin/AdminDashboard/booking-trends", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "GroupBy", "Type": "System.String", "IsRequired": false}, {"Name": "TripId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "ClearApplicationCache", "RelativePath": "api/v1/admin/AdminDashboard/clear-cache", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetSystemConfigurations", "RelativePath": "api/v1/admin/AdminDashboard/configurations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "UpdateSystemConfiguration", "RelativePath": "api/v1/admin/AdminDashboard/configurations", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.UpdateConfigurationRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "CreateSystemBackup", "RelativePath": "api/v1/admin/AdminDashboard/create-backup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.CreateBackupRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetDatabaseStatistics", "RelativePath": "api/v1/admin/AdminDashboard/database-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetErrorLogs", "RelativePath": "api/v1/admin/AdminDashboard/error-logs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "ErrorType", "Type": "System.String", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "ExportSystemData", "RelativePath": "api/v1/admin/AdminDashboard/export-data", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.ExportSystemDataRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetSystemHealthStatus", "RelativePath": "api/v1/admin/AdminDashboard/health-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetMaintenanceModeStatus", "RelativePath": "api/v1/admin/AdminDashboard/maintenance-mode", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetDashboardOverview", "RelativePath": "api/v1/admin/AdminDashboard/overview", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetPerformanceMetrics", "RelativePath": "api/v1/admin/AdminDashboard/performance-metrics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "MetricType", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetPopularDestinations", "RelativePath": "api/v1/admin/AdminDashboard/popular-destinations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Limit", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetRecentActivities", "RelativePath": "api/v1/admin/AdminDashboard/recent-activities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Limit", "Type": "System.Int32", "IsRequired": false}, {"Name": "ActivityType", "Type": "System.String", "IsRequired": false}, {"Name": "Since", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetRevenueAnalytics", "RelativePath": "api/v1/admin/AdminDashboard/revenue-analytics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "GroupBy", "Type": "System.String", "IsRequired": false}, {"Name": "TripId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PaymentMethod", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "SendSystemNotification", "RelativePath": "api/v1/admin/AdminDashboard/send-notification", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.SendSystemNotificationRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetServerMetrics", "RelativePath": "api/v1/admin/AdminDashboard/server-metrics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetSystemStatistics", "RelativePath": "api/v1/admin/AdminDashboard/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "ToggleMaintenanceMode", "RelativePath": "api/v1/admin/AdminDashboard/toggle-maintenance-mode", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.ToggleMaintenanceModeRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminDashboardController", "Method": "GetUserAnalytics", "RelativePath": "api/v1/admin/AdminDashboard/user-analytics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "GroupBy", "Type": "System.String", "IsRequired": false}, {"Name": "Role", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "GetTrips", "RelativePath": "api/v1/admin/AdminTrips", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CityId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsFeatured", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "CreateTrip", "RelativePath": "api/v1/admin/AdminTrips", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Trip.CreateTripRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "GetTrip", "RelativePath": "api/v1/admin/AdminTrips/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "UpdateTrip", "RelativePath": "api/v1/admin/AdminTrips/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Trip.UpdateTripRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "DeleteTrip", "RelativePath": "api/v1/admin/AdminTrips/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "GetTripBookings", "RelativePath": "api/v1/admin/AdminTrips/{id}/bookings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "DuplicateTrip", "RelativePath": "api/v1/admin/AdminTrips/{id}/duplicate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "FeatureTrip", "RelativePath": "api/v1/admin/AdminTrips/{id}/feature", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "UploadTripImages", "RelativePath": "api/v1/admin/AdminTrips/{id}/images", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "files", "Type": "System.Collections.Generic.IList`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "DeleteTripImage", "RelativePath": "api/v1/admin/AdminTrips/{id}/images/{imageId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "imageId", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "PublishTrip", "RelativePath": "api/v1/admin/AdminTrips/{id}/publish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "GetTripReviews", "RelativePath": "api/v1/admin/AdminTrips/{id}/reviews", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "GetTripStatistics", "RelativePath": "api/v1/admin/AdminTrips/{id}/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "UnfeatureTrip", "RelativePath": "api/v1/admin/AdminTrips/{id}/unfeature", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "UnpublishTrip", "RelativePath": "api/v1/admin/AdminTrips/{id}/unpublish", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "BulkUpdateTrips", "RelativePath": "api/v1/admin/AdminTrips/bulk-update", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.BulkUpdateTripsRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourismAPI.Controllers.Admin.AdminTripsController", "Method": "ExportTrips", "RelativePath": "api/v1/admin/AdminTrips/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Format", "Type": "System.String", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CityId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsFeatured", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StartDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Fields", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "GetUsers", "RelativePath": "api/v1/admin/AdminUsers", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "Role", "Type": "System.String", "IsRequired": false}, {"Name": "IsActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedDateFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CreatedDateTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "CreateUser", "RelativePath": "api/v1/admin/AdminUsers", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.CreateUserRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "GetUser", "RelativePath": "api/v1/admin/AdminUsers/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "UpdateUser", "RelativePath": "api/v1/admin/AdminUsers/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.UpdateUserRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "DeleteUser", "RelativePath": "api/v1/admin/AdminUsers/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "ActivateUser", "RelativePath": "api/v1/admin/AdminUsers/{id}/activate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "GetUserBookings", "RelativePath": "api/v1/admin/AdminUsers/{id}/bookings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "DeactivateUser", "RelativePath": "api/v1/admin/AdminUsers/{id}/deactivate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "SendNotification", "RelativePath": "api/v1/admin/AdminUsers/{id}/notifications", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.SendNotificationRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "ResetPassword", "RelativePath": "api/v1/admin/AdminUsers/{id}/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "AssignRole", "RelativePath": "api/v1/admin/AdminUsers/{id}/roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Admin.AssignRoleRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "RemoveRole", "RelativePath": "api/v1/admin/AdminUsers/{id}/roles/{roleName}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "<PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.Controllers.Admin.AdminUsersController", "Method": "GetUserStatistics", "RelativePath": "api/v1/admin/AdminUsers/{id}/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.Admin.AdminController", "Method": "Health", "RelativePath": "api/v1/admin/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.AuthController", "Method": "ForgotPassword", "RelativePath": "api/v1/Auth/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "forgotPasswordDto", "Type": "TravelTourism.Application.DTOs.Auth.ForgotPasswordDto", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/v1/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "TravelTourism.Application.DTOs.Auth.LoginDto", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Auth.AuthResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "TravelTourism.Application.DTOs.Auth.AuthResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.AuthController", "Method": "Logout", "RelativePath": "api/v1/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.AuthController", "Method": "RefreshToken", "RelativePath": "api/v1/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshTokenDto", "Type": "TravelTourism.Application.DTOs.Auth.RefreshTokenDto", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Auth.AuthResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "TravelTourism.Application.DTOs.Auth.AuthResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.AuthController", "Method": "Register", "RelativePath": "api/v1/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registerDto", "Type": "TravelTourism.Application.DTOs.Auth.RegisterDto", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Auth.AuthResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "TravelTourism.Application.DTOs.Auth.AuthResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.AuthController", "Method": "ResetPassword", "RelativePath": "api/v1/Auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "resetPasswordDto", "Type": "TravelTourism.Application.DTOs.Auth.ResetPasswordDto", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.AuthController", "Method": "RevokeAllTokens", "RelativePath": "api/v1/Auth/revoke-all-tokens", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.AuthController", "Method": "VerifyEmail", "RelativePath": "api/v1/Auth/verify-email", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "verifyEmailDto", "Type": "TravelTourism.Application.DTOs.Auth.VerifyEmailDto", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.BlogsController", "Method": "GetBlogs", "RelativePath": "api/v1/Blogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}, {"Name": "category", "Type": "System.String", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDirection", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BlogsController", "Method": "GetBlog", "RelativePath": "api/v1/Blogs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BlogsController", "Method": "GetRelatedBlogs", "RelativePath": "api/v1/Blogs/{id}/related", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "count", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BlogsController", "Method": "GetBlogCategories", "RelativePath": "api/v1/Blogs/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BlogsController", "Method": "GetBlogsByCategory", "RelativePath": "api/v1/Blogs/category/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BlogsController", "Method": "GetFeaturedBlogs", "RelativePath": "api/v1/Blogs/featured", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BookingsController", "Method": "CreateBooking", "RelativePath": "api/v1/Bookings", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "TravelTourism.Application.DTOs.Booking.CreateBookingRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BookingsController", "Method": "GetBooking", "RelativePath": "api/v1/Bookings/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BookingsController", "Method": "CancelBooking", "RelativePath": "api/v1/Bookings/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BookingsController", "Method": "UpdateBooking", "RelativePath": "api/v1/Bookings/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Booking.UpdateBookingRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BookingsController", "Method": "GetInvoice", "RelativePath": "api/v1/Bookings/{id}/invoice", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BookingsController", "Method": "ProcessPayment", "RelativePath": "api/v1/Bookings/{id}/payment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Core.Interfaces.Services.PaymentRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BookingsController", "Method": "ConfirmPayment", "RelativePath": "api/v1/Bookings/{id}/payment/confirm", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Booking.PaymentConfirmationRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BookingsController", "Method": "GetPaymentStatus", "RelativePath": "api/v1/Bookings/{id}/payment/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "paymentIntentId", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.BookingsController", "Method": "RequestRefund", "RelativePath": "api/v1/Bookings/{id}/refund", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "TravelTourism.Application.DTOs.Booking.RefundRequest", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.CitiesController", "Method": "GetCities", "RelativePath": "api/v1/Cities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "countryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.CitiesController", "Method": "GetCity", "RelativePath": "api/v1/Cities/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.CountriesController", "Method": "GetCountries", "RelativePath": "api/v1/Countries", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.CountriesController", "Method": "GetCountry", "RelativePath": "api/v1/Countries/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.FilesController", "Method": "UploadDocument", "RelativePath": "api/v1/Files/upload-document", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}, {"Name": "folder", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.FilesController", "Method": "UploadImage", "RelativePath": "api/v1/Files/upload-image", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ContentType", "Type": "System.String", "IsRequired": false}, {"Name": "ContentDisposition", "Type": "System.String", "IsRequired": false}, {"Name": "Headers", "Type": "Microsoft.AspNetCore.Http.IHeaderDictionary", "IsRequired": false}, {"Name": "Length", "Type": "System.Int64", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "FileName", "Type": "System.String", "IsRequired": false}, {"Name": "folder", "Type": "System.String", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.TripsController", "Method": "GetTrips", "RelativePath": "api/v1/Trips", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "CategoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DestinationId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DestinationCityId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DepartureCityId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "System.String", "IsRequired": false}, {"Name": "DifficultyLevel", "Type": "System.String", "IsRequired": false}, {"Name": "MinPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinDuration", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxDuration", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AvailableFrom", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "AvailableTo", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IncludesAccommodation", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IncludesTransport", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IncludesMeals", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IncludesGuide", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsFeatured", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortOrder", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}, {"Name": "Take", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}, {"Name": "Take", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Common.ApiResponse`1[[TravelTourism.Application.DTOs.Common.PagedResult`1[[TravelTourism.Application.DTOs.Trip.TripDto, TravelTourism.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], TravelTourism.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.TripsController", "Method": "GetTrip", "RelativePath": "api/v1/Trips/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Common.ApiResponse`1[[TravelTourism.Application.DTOs.Trip.TripDetailDto, TravelTourism.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.TripsController", "Method": "CheckAvailability", "RelativePath": "api/v1/Trips/{tripId}/availability", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tripId", "Type": "System.Int32", "IsRequired": true}, {"Name": "travelDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "numberOfPeople", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Common.ApiResponse`1[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.TripsController", "Method": "GetCategories", "RelativePath": "api/v1/Trips/categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Common.ApiResponse`1[[System.Collections.Generic.List`1[[TravelTourism.Application.DTOs.Trip.TripCategoryDto, TravelTourism.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.TripsController", "Method": "GetTripsByCategory", "RelativePath": "api/v1/Trips/category/{categoryId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "categoryId", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Common.ApiResponse`1[[System.Collections.Generic.List`1[[TravelTourism.Application.DTOs.Trip.TripDto, TravelTourism.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.TripsController", "Method": "GetTripsByDestination", "RelativePath": "api/v1/Trips/destination/{destinationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "destinationId", "Type": "System.Int32", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Common.ApiResponse`1[[System.Collections.Generic.List`1[[TravelTourism.Application.DTOs.Trip.TripDto, TravelTourism.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.TripsController", "Method": "GetDestinations", "RelativePath": "api/v1/Trips/destinations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Common.ApiResponse`1[[System.Collections.Generic.List`1[[TravelTourism.Application.DTOs.Common.CityDto, TravelTourism.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.TripsController", "Method": "GetFeaturedTrips", "RelativePath": "api/v1/Trips/featured", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Common.ApiResponse`1[[System.Collections.Generic.List`1[[TravelTourism.Application.DTOs.Trip.TripDto, TravelTourism.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.TripsController", "Method": "SearchTrips", "RelativePath": "api/v1/Trips/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "categoryId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "destinationId", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "TravelTourism.Application.DTOs.Common.ApiResponse`1[[System.Collections.Generic.List`1[[TravelTourism.Application.DTOs.Trip.TripDto, TravelTourism.Application, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "TravelTourism.API.Controllers.V1.UsersController", "Method": "DeleteAccount", "RelativePath": "api/v1/Users/<USER>", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.UsersController", "Method": "GetUserBookings", "RelativePath": "api/v1/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.UsersController", "Method": "GetProfile", "RelativePath": "api/v1/Users/<USER>", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "TravelTourism.API.Controllers.V1.UsersController", "Method": "UpdateProfile", "RelativePath": "api/v1/Users/<USER>", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateUserDto", "Type": "TravelTourism.Application.DTOs.User.UpdateUserDto", "IsRequired": true}, {"Name": "api-version", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}]