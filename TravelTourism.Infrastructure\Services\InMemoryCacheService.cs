using Microsoft.Extensions.Caching.Memory;
using System.Text.Json;
using TravelTourism.Core.Interfaces.Services;

namespace TravelTourism.Infrastructure.Services;

public class InMemoryCacheService : ICacheService
{
    private readonly IMemoryCache _memoryCache;

    public InMemoryCacheService(IMemoryCache memoryCache)
    {
        _memoryCache = memoryCache;
    }

    public async Task<T?> GetAsync<T>(string key) where T : class
    {
        if (_memoryCache.TryGetValue(key, out var value))
        {
            if (value is string jsonString)
            {
                return JsonSerializer.Deserialize<T>(jsonString);
            }
            return (T?)value;
        }
        return default(T);
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan? expiry = null) where T : class
    {
        var options = new MemoryCacheEntryOptions();

        if (expiry.HasValue)
        {
            options.AbsoluteExpirationRelativeToNow = expiry;
        }
        else
        {
            // Default expiration of 1 hour
            options.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(1);
        }

        // Store as JSON string to ensure consistency with Redis behavior
        var jsonString = JsonSerializer.Serialize(value);
        _memoryCache.Set(key, jsonString, options);
    }

    public async Task<bool> RemoveAsync(string key)
    {
        _memoryCache.Remove(key);
        return true; // Memory cache remove doesn't return a boolean, so we assume success
    }

    public async Task<bool> ExistsAsync(string key)
    {
        return _memoryCache.TryGetValue(key, out _);
    }

    public async Task SetHashAsync<T>(string key, string field, T value) where T : class
    {
        // Simulate hash by using a composite key
        var hashKey = $"{key}:{field}";
        await SetAsync(hashKey, value);
    }

    public async Task<T?> GetHashAsync<T>(string key, string field) where T : class
    {
        // Simulate hash by using a composite key
        var hashKey = $"{key}:{field}";
        return await GetAsync<T>(hashKey);
    }

    public async Task<bool> RemoveHashAsync(string key, string field)
    {
        // Simulate hash by using a composite key
        var hashKey = $"{key}:{field}";
        return await RemoveAsync(hashKey);
    }

    public async Task SetListAsync<T>(string key, IEnumerable<T> values, TimeSpan? expiry = null) where T : class
    {
        var list = values.ToList();
        await SetAsync(key, list, expiry);
    }

    public async Task<List<T>> GetListAsync<T>(string key, long start = 0, long stop = -1) where T : class
    {
        var list = await GetAsync<List<T>>(key);
        if (list == null) return new List<T>();

        // Simulate Redis list slicing
        if (stop == -1) stop = list.Count - 1;
        if (start < 0) start = Math.Max(0, list.Count + start);
        if (stop < 0) stop = Math.Max(0, list.Count + stop);

        var startIndex = (int)Math.Max(0, start);
        var endIndex = (int)Math.Min(list.Count - 1, stop);

        if (startIndex > endIndex) return new List<T>();

        return list.Skip(startIndex).Take(endIndex - startIndex + 1).ToList();
    }

    public async Task<bool> SetExpiryAsync(string key, TimeSpan expiry)
    {
        // Memory cache doesn't support setting expiry on existing items
        // This is a limitation compared to Redis
        return false;
    }

    public async Task<TimeSpan?> GetExpiryAsync(string key)
    {
        // Memory cache doesn't expose TTL information
        // This is a limitation compared to Redis
        return null;
    }

    public async Task<bool> ClearAllAsync()
    {
        // Memory cache doesn't have a clear all method
        // This is a limitation compared to Redis
        // We could implement this by keeping track of all keys, but for simplicity, we'll return false
        return false;
    }

    public async Task<long> GetDatabaseSizeAsync()
    {
        // Memory cache doesn't expose size information
        // This is a limitation compared to Redis
        return 0;
    }
}
